"""
OpenAI Embeddings

This module provides OpenAI embedding functionality.
"""

import logging
from typing import List, Dict, Any, Optional
import openai
from ...app.core.logging import get_logger
# from ..utils.timeout_retry import embedding_operation  # Temporarily commented out
from .base import EmbeddingModel

logger = get_logger(__name__)

class OpenAIEmbedding(EmbeddingModel):
    """
    OpenAI embedding model implementation.
    
    This class provides functionality for:
    1. Text embedding generation
    2. Batch embedding processing
    3. Embedding caching
    """
    
    def __init__(
        self,
        model_name: str = "text-embedding-3-small",
        dimension: int = 1536,
        **kwargs
    ):
        """
        Initialize OpenAI embedding model.
        
        Args:
            model_name: Name of the OpenAI embedding model
            dimension: Dimension of the embedding vectors
            **kwargs: Additional arguments
        """
        self.model_name = model_name
        self.dimension = dimension
        self.client = openai.OpenAI(**kwargs)

    # @embedding_operation  # Temporarily commented out
    async def embed_document(
        self,
        text: str,
        **kwargs
    ) -> List[float]:
        """
        Generate embedding for a document.
        
        Args:
            text: Document text
            **kwargs: Additional arguments
            
        Returns:
            Embedding vector
        """
        try:
            response = await self.client.embeddings.create(
                model=self.model_name,
                input=text,
                **kwargs
            )
            return response.data[0].embedding
            
        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")
            raise

    @embedding_operation
    async def embed_documents(
        self,
        texts: List[str],
        **kwargs
    ) -> List[List[float]]:
        """
        Generate embeddings for multiple documents.
        
        Args:
            texts: List of document texts
            **kwargs: Additional arguments
            
        Returns:
            List of embedding vectors
        """
        try:
            response = await self.client.embeddings.create(
                model=self.model_name,
                input=texts,
                **kwargs
            )
            return [data.embedding for data in response.data]
            
        except Exception as e:
            logger.error(f"Error generating embeddings: {str(e)}")
            raise

    @embedding_operation
    async def embed_query(
        self,
        text: str,
        **kwargs
    ) -> List[float]:
        """
        Generate embedding for a query.
        
        Args:
            text: Query text
            **kwargs: Additional arguments
            
        Returns:
            Embedding vector
        """
        try:
            response = await self.client.embeddings.create(
                model=self.model_name,
                input=text,
                **kwargs
            )
            return response.data[0].embedding
            
        except Exception as e:
            logger.error(f"Error generating query embedding: {str(e)}")
            raise